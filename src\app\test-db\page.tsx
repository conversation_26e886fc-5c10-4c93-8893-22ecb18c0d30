'use client'

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { getAvailableCategories, insertSampleProducts, clearAllProducts } from '@/utils/testData';
import { supabase } from '@/lib/supabase';

export default function TestDbPage() {
  const [results, setResults] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const addResult = (message: string) => {
    setResults(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  const testConnection = async () => {
    setIsLoading(true);
    addResult('Testing database connection...');
    
    try {
      const { data, error } = await supabase.from('shop_subcategories').select('count').limit(1);
      if (error) {
        addResult(`❌ Connection failed: ${error.message}`);
      } else {
        addResult('✅ Database connection successful');
      }
    } catch (error) {
      addResult(`❌ Connection error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    setIsLoading(false);
  };

  const testCategories = async () => {
    setIsLoading(true);
    addResult('Fetching categories...');
    
    try {
      const result = await getAvailableCategories();
      if (result.success) {
        addResult(`✅ Found ${result.data.length} categories`);
        result.data.forEach(cat => {
          addResult(`  - ${cat.name} (ID: ${cat.id})`);
        });
      } else {
        addResult(`❌ Failed to fetch categories: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    setIsLoading(false);
  };

  const testProducts = async () => {
    setIsLoading(true);
    addResult('Fetching existing products...');
    
    try {
      const { data, error } = await supabase
        .from('shop_products')
        .select('id, name, categories, is_active')
        .limit(10);
      
      if (error) {
        addResult(`❌ Failed to fetch products: ${error.message}`);
      } else {
        addResult(`✅ Found ${data?.length || 0} products`);
        data?.forEach(product => {
          addResult(`  - ${product.name} (Category: ${product.categories})`);
        });
      }
    } catch (error) {
      addResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    setIsLoading(false);
  };

  const insertSampleData = async () => {
    setIsLoading(true);
    addResult('Inserting sample products...');
    
    try {
      const result = await insertSampleProducts();
      if (result.success) {
        addResult(`✅ Inserted ${result.data?.length || 0} sample products`);
      } else {
        addResult(`❌ Failed to insert products: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    setIsLoading(false);
  };

  const clearProducts = async () => {
    setIsLoading(true);
    addResult('Clearing all products...');
    
    try {
      const result = await clearAllProducts();
      if (result.success) {
        addResult('✅ All products cleared');
      } else {
        addResult(`❌ Failed to clear products: ${result.error}`);
      }
    } catch (error) {
      addResult(`❌ Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
    
    setIsLoading(false);
  };

  const clearResults = () => {
    setResults([]);
  };

  return (
    <div className="min-h-screen bg-gray-50 p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">Database Test Page</h1>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Controls */}
          <Card>
            <CardHeader>
              <CardTitle>Database Operations</CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={testConnection} 
                disabled={isLoading}
                className="w-full"
              >
                Test Connection
              </Button>
              
              <Button 
                onClick={testCategories} 
                disabled={isLoading}
                className="w-full"
              >
                Test Categories
              </Button>
              
              <Button 
                onClick={testProducts} 
                disabled={isLoading}
                className="w-full"
              >
                Test Products
              </Button>
              
              <Button 
                onClick={insertSampleData} 
                disabled={isLoading}
                className="w-full bg-green-600 hover:bg-green-700"
              >
                Insert Sample Data
              </Button>
              
              <Button 
                onClick={clearProducts} 
                disabled={isLoading}
                variant="destructive"
                className="w-full"
              >
                Clear All Products
              </Button>
              
              <Button 
                onClick={clearResults} 
                disabled={isLoading}
                variant="outline"
                className="w-full"
              >
                Clear Results
              </Button>
            </CardContent>
          </Card>

          {/* Results */}
          <Card>
            <CardHeader>
              <CardTitle>Results</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="bg-gray-900 text-green-400 p-4 rounded-lg h-96 overflow-y-auto font-mono text-sm">
                {results.length === 0 ? (
                  <div className="text-gray-500">No results yet. Click a button to start testing.</div>
                ) : (
                  results.map((result, index) => (
                    <div key={index} className="mb-1">
                      {result}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>
        </div>
        
        <div className="mt-8">
          <Card>
            <CardHeader>
              <CardTitle>Instructions</CardTitle>
            </CardHeader>
            <CardContent>
              <ol className="list-decimal list-inside space-y-2 text-gray-700">
                <li>First, test the database connection</li>
                <li>Check if categories exist in the database</li>
                <li>Check if products exist in the database</li>
                <li>If no products exist, insert sample data</li>
                <li>Go back to the home page and test category navigation</li>
              </ol>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
