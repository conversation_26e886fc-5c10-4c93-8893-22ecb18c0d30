'use client'

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import Image from 'next/image';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/contexts/CartContext';
import { useAuth } from '@/contexts/AuthContext';
import { AuthModal } from '@/components/auth/AuthModal';
import {
  Search,
  MapPin,
  ShoppingCart,
  Menu,
  ChevronDown,
  User,
  Heart,
  Bell,
  LogOut,
  Building2,
  X
} from 'lucide-react';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

export function Header() {
  const router = useRouter();
  const { getTotalItems } = useCart();
  const { user, signOut } = useAuth();
  const [showMobileBanner, setShowMobileBanner] = useState(true);
  const [currentLocation, setCurrentLocation] = useState<string>("Select Location");
  const [isDetectingLocation, setIsDetectingLocation] = useState(false);
  const [locationError, setLocationError] = useState<string>("");
  const [showAuthModal, setShowAuthModal] = useState(false);
  const [showMobileMenu, setShowMobileMenu] = useState(false);

  const handleUseAppClick = () => {
    // Handle app download/redirect logic
    console.log('Use App clicked');
  };

  const handleCartClick = () => {
    if (!user) {
      setShowAuthModal(true);
    } else {
      // Navigate to cart page or show cart sidebar
      router.push('/cart');
    }
  };

  const handleUserClick = () => {
    if (!user) {
      setShowAuthModal(true);
    } else {
      // Navigate to user profile
      router.push('/profile');
    }
  };

  const handleSignOut = async () => {
    await signOut();
  };

  // Function to reverse geocode coordinates to get address
  const reverseGeocode = async (lat: number, lng: number): Promise<string> => {
    try {
      // Use OpenStreetMap's free Nominatim service
      const response = await fetch(
        `https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}&zoom=10&addressdetails=1`,
        {
          headers: {
            'User-Agent': 'Infratask-App/1.0'
          }
        }
      );

      if (response.ok) {
        const data = await response.json();

        // Prioritize more specific local area information
        const neighborhood = data.address?.neighbourhood || data.address?.suburb || '';
        const district = data.address?.city_district || data.address?.district || '';
        const city = data.address?.city || data.address?.town || data.address?.village || '';
        const municipality = data.address?.municipality || '';
        const county = data.address?.county || '';
        const state = data.address?.state || '';

        // Return the most specific location available, prioritizing local areas
        if (neighborhood && city) {
          return `${neighborhood}, ${city}`;
        } else if (district && city) {
          return `${district}, ${city}`;
        } else if (municipality && city) {
          return `${municipality}, ${city}`;
        } else if (city && county && county !== city) {
          return `${city}, ${county}`;
        } else if (city) {
          return city;
        } else if (neighborhood) {
          return neighborhood;
        } else if (district) {
          return district;
        } else if (municipality) {
          return municipality;
        } else if (county) {
          return county;
        } else if (data.display_name) {
          // Extract the first two meaningful parts of the display name
          const parts = data.display_name.split(',').slice(0, 2);
          return parts.join(',').trim();
        }
      }
      throw new Error('Geocoding failed');
    } catch (error) {
      console.error('Geocoding error:', error);
      return 'Location detected';
    }
  };

  const detectLocation = async () => {
    setIsDetectingLocation(true);
    setLocationError("");

    if (!navigator.geolocation) {
      setLocationError("Geolocation is not supported by this browser.");
      setIsDetectingLocation(false);
      return;
    }

    navigator.geolocation.getCurrentPosition(
      async (position) => {
        try {
          const { latitude, longitude } = position.coords;
          const address = await reverseGeocode(latitude, longitude);
          setCurrentLocation(address);
          setLocationError("");
        } catch (error) {
          console.error('Error getting location:', error);
          setCurrentLocation('Location detected');
          setLocationError("Failed to get location details.");
        } finally {
          setIsDetectingLocation(false);
        }
      },
      (error) => {
        console.error('Geolocation error:', error);
        setIsDetectingLocation(false);
        switch (error.code) {
          case error.PERMISSION_DENIED:
            setLocationError("Location access denied by user.");
            break;
          case error.POSITION_UNAVAILABLE:
            setLocationError("Location information is unavailable.");
            break;
          case error.TIMEOUT:
            setLocationError("Location request timed out.");
            break;
          default:
            setLocationError("Unable to retrieve your location.");
            break;
        }
      },
      {
        enableHighAccuracy: true,
        timeout: 10000,
        maximumAge: 300000
      }
    );
  };

  return (
    <>
      {/* Mobile-Only App Download Banner */}
      {showMobileBanner && (
        <div className="md:hidden bg-green-600 text-white px-4 py-3 sticky top-0 z-50 shadow-sm">
          <div className="flex items-center justify-between">
            {/* Close button on the left */}
            <button
              onClick={() => setShowMobileBanner(false)}
              className="flex items-center justify-center w-6 h-6 rounded-full hover:bg-green-700 transition-colors"
              aria-label="Close banner"
            >
              <X className="h-4 w-4" />
            </button>
            
            {/* Center content */}
            <div className="flex items-center space-x-2 flex-1 justify-center">
              <Image
                src="/favicon.png"
                alt="Infratask"
                width={20}
                height={20}
                className="rounded-sm"
              />
              <span className="text-sm font-medium">Get The App for Better Experience</span>
            </div>
            
            {/* Use App button on the right */}
            <Button 
              size="sm" 
              onClick={handleUseAppClick}
              className="bg-black hover:bg-gray-800 text-white rounded-lg px-4 py-1"
            >
              Use App
            </Button>
          </div>
        </div>
      )}

      {/* Enhanced Fixed Header */}
      <header className={`bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-100 fixed left-0 right-0 z-40 ${showMobileBanner ? 'md:top-0 top-12' : 'top-0'}`}>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            {/* Logo and Location - Hidden on mobile */}
            <div className="hidden md:flex items-center space-x-4">
              <div className="flex items-center">
                <Image
                  src="/logo.png"
                  alt="Infratask Logo"
                  width={40}
                  height={40}
                  className="mr-3 rounded-lg"
                />
                <div className="text-2xl font-bold text-[#f97316]">
                  Infratask
                </div>
              </div>

              {/* Location Selector */}
              <div className="relative">
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={detectLocation}
                  disabled={isDetectingLocation}
                  className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl max-w-48 truncate"
                >
                  <MapPin className="h-4 w-4 mr-1 flex-shrink-0" />
                  <span className="truncate text-sm">
                    {isDetectingLocation ? "Detecting..." : currentLocation}
                  </span>
                  <ChevronDown className="h-3 w-3 ml-1 flex-shrink-0" />
                </Button>
                {locationError && (
                  <div className="absolute top-full mt-1 text-xs text-red-600 bg-white p-2 rounded shadow-lg border z-50">
                    {locationError}
                  </div>
                )}
              </div>
            </div>

            {/* Mobile Header - Only Location and Menu */}
            <div className="md:hidden flex items-center justify-between w-full">
              {/* Mobile Location Selector */}
              <Button
                variant="ghost"
                size="sm"
                onClick={detectLocation}
                disabled={isDetectingLocation}
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl flex-1 max-w-48 justify-start"
              >
                <MapPin className="h-4 w-4 mr-2 flex-shrink-0" />
                <span className="truncate text-sm">
                  {isDetectingLocation ? "Detecting..." : currentLocation}
                </span>
                <ChevronDown className="h-3 w-3 ml-2 flex-shrink-0" />
              </Button>

              {/* Mobile Menu Button */}
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowMobileMenu(true)}
                className="p-2"
              >
                <Menu className="h-5 w-5" />
              </Button>
            </div>

            {/* Search Bar and Project Management - Hidden on mobile */}
            <div className="hidden md:flex flex-1 max-w-3xl mx-8 items-center space-x-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <input
                  type="text"
                  placeholder="Search for construction materials, tools, equipment..."
                  className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#f97316] focus:border-transparent bg-gray-50 hover:bg-white transition-colors"
                />
              </div>

              {/* Project Management Button */}
              <Button
                variant="ghost"
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl whitespace-nowrap"
                onClick={() => router.push('/project-management')}
              >
                Project Management
              </Button>
            </div>

            {/* Right Side Actions */}
            <div className="flex items-center space-x-2">
              {/* Authentication-based UI */}
              {user ? (
                <>
                  {/* User Account - Authenticated with Dropdown */}
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl p-2"
                        title="Account"
                      >
                        <User className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-48 rounded-xl shadow-lg" align="end">
                      <DropdownMenuItem
                        className="rounded-lg cursor-pointer hover:bg-orange-50"
                        onClick={() => router.push('/profile')}
                      >
                        <User className="mr-2 h-4 w-4" />
                        <span>Profile</span>
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        className="rounded-lg cursor-pointer hover:bg-orange-50"
                        onClick={() => router.push('/cart')}
                      >
                        <ShoppingCart className="mr-2 h-4 w-4" />
                        <span>My Cart</span>
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem
                        className="rounded-lg cursor-pointer hover:bg-red-50 text-red-600"
                        onClick={handleSignOut}
                      >
                        <LogOut className="mr-2 h-4 w-4" />
                        <span>Sign Out</span>
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>

                  {/* Wishlist - Only for authenticated users */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
                    title="Wishlist"
                  >
                    <Heart className="h-4 w-4" />
                    <Badge className="absolute -top-1 -right-1 bg-[#f97316] text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                      2
                    </Badge>
                  </Button>

                  {/* Notifications - Only for authenticated users */}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
                    title="Notifications"
                  >
                    <Bell className="h-4 w-4" />
                    <Badge className="absolute -top-1 -right-1 bg-red-500 text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                      1
                    </Badge>
                  </Button>
                </>
              ) : (
                <>
                  {/* Login Button - Desktop */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleUserClick}
                    className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl hidden sm:inline-flex"
                  >
                    <User className="h-4 w-4 mr-2" />
                    Login
                  </Button>

                  {/* Login Button - Mobile */}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={handleUserClick}
                    className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl sm:hidden p-2"
                    title="Login"
                  >
                    <User className="h-4 w-4" />
                  </Button>
                </>
              )}
              
              {/* Shopping Cart */}
              <Button
                variant="ghost"
                size="sm"
                onClick={handleCartClick}
                className="text-gray-600 hover:text-[#f97316] hover:bg-orange-50 rounded-xl relative p-2"
              >
                <ShoppingCart className="h-4 w-4" />
                <Badge className="absolute -top-1 -right-1 bg-[#f97316] text-white rounded-full h-5 w-5 text-xs flex items-center justify-center">
                  {getTotalItems()}
                </Badge>
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Sidebar Menu */}
      {showMobileMenu && (
        <div className="fixed inset-0 z-50 md:hidden">
          {/* Backdrop */}
          <div
            className="fixed inset-0 bg-black bg-opacity-50"
            onClick={() => setShowMobileMenu(false)}
          />

          {/* Sidebar */}
          <div className="fixed right-0 top-0 h-full w-80 bg-white shadow-xl">
            <div className="flex flex-col h-full">
              {/* Header */}
              <div className="flex items-center justify-between p-4 border-b">
                <div className="flex items-center">
                  <Image
                    src="/logo.png"
                    alt="Infratask Logo"
                    width={32}
                    height={32}
                    className="mr-2 rounded-lg"
                  />
                  <span className="text-lg font-bold text-[#f97316]">Infratask</span>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowMobileMenu(false)}
                >
                  <X className="h-5 w-5" />
                </Button>
              </div>

              {/* Search Bar */}
              <div className="p-4 border-b">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <input
                    type="text"
                    placeholder="Search materials..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-[#f97316] focus:border-transparent"
                  />
                </div>
              </div>

              {/* Menu Items */}
              <div className="flex-1 p-4 space-y-2">
                {/* Project Management */}
                <Button
                  variant="ghost"
                  className="w-full justify-start text-left"
                  onClick={() => {
                    router.push('/project-management');
                    setShowMobileMenu(false);
                  }}
                >
                  <Building2 className="h-4 w-4 mr-3" />
                  Project Management
                </Button>

                {/* Authentication-based menu items */}
                {user ? (
                  <>
                    <Button
                      variant="ghost"
                      className="w-full justify-start text-left"
                      onClick={() => {
                        router.push('/profile');
                        setShowMobileMenu(false);
                      }}
                    >
                      <User className="h-4 w-4 mr-3" />
                      Profile
                    </Button>

                    <Button
                      variant="ghost"
                      className="w-full justify-start text-left"
                      onClick={() => {
                        router.push('/cart');
                        setShowMobileMenu(false);
                      }}
                    >
                      <ShoppingCart className="h-4 w-4 mr-3" />
                      My Cart
                      {getTotalItems() > 0 && (
                        <Badge className="ml-auto bg-[#f97316] text-white">
                          {getTotalItems()}
                        </Badge>
                      )}
                    </Button>

                    <Button
                      variant="ghost"
                      className="w-full justify-start text-left"
                    >
                      <Heart className="h-4 w-4 mr-3" />
                      Wishlist
                      <Badge className="ml-auto bg-[#f97316] text-white">
                        2
                      </Badge>
                    </Button>

                    <Button
                      variant="ghost"
                      className="w-full justify-start text-left"
                    >
                      <Bell className="h-4 w-4 mr-3" />
                      Notifications
                      <Badge className="ml-auto bg-red-500 text-white">
                        1
                      </Badge>
                    </Button>
                  </>
                ) : (
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-left"
                    onClick={() => {
                      setShowAuthModal(true);
                      setShowMobileMenu(false);
                    }}
                  >
                    <User className="h-4 w-4 mr-3" />
                    Login
                  </Button>
                )}
              </div>

              {/* Footer */}
              {user && (
                <div className="p-4 border-t">
                  <Button
                    variant="ghost"
                    className="w-full justify-start text-left text-red-600 hover:text-red-700 hover:bg-red-50"
                    onClick={() => {
                      handleSignOut();
                      setShowMobileMenu(false);
                    }}
                  >
                    <LogOut className="h-4 w-4 mr-3" />
                    Sign Out
                  </Button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Mobile Floating Cart Button */}
      <div className="md:hidden fixed bottom-6 right-6 z-40">
        <Button
          onClick={handleCartClick}
          className="bg-[#f97316] hover:bg-[#ea580c] text-white rounded-full w-14 h-14 shadow-lg relative"
        >
          <ShoppingCart className="h-6 w-6" />
          {getTotalItems() > 0 && (
            <Badge className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full h-6 w-6 text-xs flex items-center justify-center">
              {getTotalItems()}
            </Badge>
          )}
        </Button>
      </div>

      {/* Authentication Modal */}
      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
        onSuccess={() => {
          // Optional: Show success message or redirect
          console.log('User authenticated successfully');
        }}
      />
    </>
  );
}
