'use client'

import React, { useState } from 'react';
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { PhoneLogin } from './PhoneLogin';
import { OTPVerification } from './OTPVerification';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

type AuthStep = 'phone' | 'otp';

export const AuthModal: React.FC<AuthModalProps> = ({ 
  isOpen, 
  onClose, 
  onSuccess 
}) => {
  const [currentStep, setCurrentStep] = useState<AuthStep>('phone');
  const [phoneNumber, setPhoneNumber] = useState('');

  const handleOTPSent = (phone: string) => {
    setPhoneNumber(phone);
    setCurrentStep('otp');
  };

  const handleVerified = () => {
    onSuccess?.();
    onClose();
    // Reset state for next time
    setCurrentStep('phone');
    setPhoneNumber('');
  };

  const handleBack = () => {
    setCurrentStep('phone');
  };

  const handleClose = () => {
    onClose();
    // Reset state when modal is closed
    setTimeout(() => {
      setCurrentStep('phone');
      setPhoneNumber('');
    }, 300); // Wait for modal close animation
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md p-0 gap-0">
        <DialogHeader className="sr-only">
          <DialogTitle>
            {currentStep === 'phone' ? 'Phone Login' : 'OTP Verification'}
          </DialogTitle>
        </DialogHeader>
        
        {currentStep === 'phone' ? (
          <PhoneLogin 
            onOTPSent={handleOTPSent}
            onClose={handleClose}
          />
        ) : (
          <OTPVerification
            phone={phoneNumber}
            onVerified={handleVerified}
            onBack={handleBack}
          />
        )}
      </DialogContent>
    </Dialog>
  );
};
