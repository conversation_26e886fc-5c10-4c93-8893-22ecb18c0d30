'use client'

import React, { useState, useEffect } from 'react';
import { useParams, useRouter } from 'next/navigation';
import Image from 'next/image';
import { supabase } from '@/lib/supabase';
import { Product } from '@/types/product';
import { Header } from '@/components/Header';
import { Footer } from '@/components/Footer';
import { ProductCard } from '@/components/ProductCard';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { useCart } from '@/contexts/CartContext';
import { 
  ShoppingCart, 
  Plus, 
  Minus, 
  Star, 
  Truck, 
  Shield, 
  ArrowLeft,
  Heart,
  Share2
} from 'lucide-react';

export default function ProductDetailPage() {
  const params = useParams();
  const router = useRouter();
  const productId = params.productId as string;
  const { items, addToCart, updateQuantity } = useCart();

  const [product, setProduct] = useState<Product | null>(null);
  const [similarProducts, setSimilarProducts] = useState<Product[]>([]);
  const [isLoadingProduct, setIsLoadingProduct] = useState(true);
  const [isLoadingSimilar, setIsLoadingSimilar] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedImageIndex, setSelectedImageIndex] = useState(0);
  const [isLoading, setIsLoading] = useState(false);

  // Find if product is already in cart
  const cartItem = items.find(item => item.product.id === parseInt(productId));
  const quantityInCart = cartItem?.quantity || 0;

  // Fetch product details
  const fetchProduct = async () => {
    try {
      setIsLoadingProduct(true);
      setError(null);

      const { data, error } = await supabase
        .from('shop_products')
        .select(`
          *,
          shop_subcategories (
            id,
            name
          )
        `)
        .eq('id', productId)
        .single();

      if (error) {
        console.error('Error fetching product:', error);
        setError(`Product not found: ${error.message}`);
        return;
      }

      setProduct(data);
    } catch (error) {
      console.error('Error fetching product:', error);
      setError(`Failed to load product: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setIsLoadingProduct(false);
    }
  };

  // Fetch similar products
  const fetchSimilarProducts = async (categoryId: number) => {
    try {
      setIsLoadingSimilar(true);

      const { data, error } = await supabase
        .from('shop_products')
        .select(`
          *,
          shop_subcategories (
            id,
            name
          )
        `)
        .eq('categories', categoryId)
        .neq('id', productId)
        .limit(8);

      if (error) {
        console.error('Error fetching similar products:', error);
        return;
      }

      setSimilarProducts(data || []);
    } catch (error) {
      console.error('Error fetching similar products:', error);
    } finally {
      setIsLoadingSimilar(false);
    }
  };

  useEffect(() => {
    if (productId) {
      fetchProduct();
    }
  }, [productId]);

  useEffect(() => {
    if (product?.categories) {
      fetchSimilarProducts(product.categories);
    }
  }, [product]);

  const handleAddToCart = async () => {
    if (!product) return;
    
    setIsLoading(true);
    try {
      addToCart(product, 1);
    } catch (error) {
      console.error('Error adding to cart:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleIncrement = () => {
    if (!product) return;
    
    if (cartItem) {
      updateQuantity(product.id, cartItem.quantity + 1);
    } else {
      addToCart(product, 1);
    }
  };

  const handleDecrement = () => {
    if (!product) return;
    
    if (cartItem && cartItem.quantity > 1) {
      updateQuantity(product.id, cartItem.quantity - 1);
    } else if (cartItem && cartItem.quantity === 1) {
      updateQuantity(product.id, 0);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  };

  const discountPercentage = product?.original_price && product.original_price > product.price
    ? Math.round(((product.original_price - product.price) / product.original_price) * 100)
    : 0;

  const productImages = product?.images || (product?.image_url ? [product.image_url] : ['/placeholder-product.svg']);

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="pt-16 flex items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Product Not Found</h1>
            <p className="text-gray-600 mb-6">{error}</p>
            <Button onClick={() => router.back()} className="bg-[#f97316] hover:bg-[#ea580c]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Go Back
            </Button>
          </div>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="pt-16">
        {/* Breadcrumb */}
        <div className="bg-white border-b">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="py-4">
              <button
                onClick={() => router.back()}
                className="flex items-center text-gray-600 hover:text-[#f97316] transition-colors"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back to Products
              </button>
            </div>
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          {isLoadingProduct ? (
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              {/* Image Skeleton */}
              <div className="space-y-4">
                <div className="aspect-square bg-gray-200 animate-pulse rounded-lg"></div>
                <div className="flex space-x-2">
                  {[...Array(4)].map((_, i) => (
                    <div key={i} className="w-16 h-16 bg-gray-200 animate-pulse rounded-lg"></div>
                  ))}
                </div>
              </div>
              
              {/* Details Skeleton */}
              <div className="space-y-4">
                <div className="h-8 bg-gray-200 animate-pulse rounded w-3/4"></div>
                <div className="h-6 bg-gray-200 animate-pulse rounded w-1/2"></div>
                <div className="h-10 bg-gray-200 animate-pulse rounded w-1/3"></div>
                <div className="h-32 bg-gray-200 animate-pulse rounded"></div>
                <div className="h-12 bg-gray-200 animate-pulse rounded"></div>
              </div>
            </div>
          ) : product ? (
            <>
              {/* Product Details Section */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
                {/* Product Images */}
                <div className="space-y-4">
                  {/* Main Image */}
                  <div className="aspect-square overflow-hidden rounded-lg bg-gray-100">
                    <Image
                      src={productImages[selectedImageIndex]}
                      alt={product.name}
                      width={600}
                      height={600}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        const target = e.target as HTMLImageElement;
                        target.src = '/placeholder-product.svg';
                      }}
                    />
                  </div>
                  
                  {/* Thumbnail Images */}
                  {productImages.length > 1 && (
                    <div className="flex space-x-2 overflow-x-auto">
                      {productImages.map((image, index) => (
                        <button
                          key={index}
                          onClick={() => setSelectedImageIndex(index)}
                          className={`flex-shrink-0 w-16 h-16 rounded-lg overflow-hidden border-2 transition-colors ${
                            selectedImageIndex === index 
                              ? 'border-[#f97316]' 
                              : 'border-gray-200 hover:border-gray-300'
                          }`}
                        >
                          <Image
                            src={image}
                            alt={`${product.name} ${index + 1}`}
                            width={64}
                            height={64}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement;
                              target.src = '/placeholder-product.svg';
                            }}
                          />
                        </button>
                      ))}
                    </div>
                  )}
                </div>

                {/* Product Information */}
                <div className="space-y-6">
                  {/* Brand */}
                  {product.brand && (
                    <p className="text-sm text-gray-500">{product.brand}</p>
                  )}

                  {/* Product Name */}
                  <h1 className="text-3xl font-bold text-gray-900">{product.name}</h1>

                  {/* Rating (placeholder) */}
                  <div className="flex items-center space-x-2">
                    <div className="flex items-center">
                      {[...Array(5)].map((_, i) => (
                        <Star key={i} className="h-5 w-5 text-yellow-400 fill-current" />
                      ))}
                    </div>
                    <span className="text-sm text-gray-600">(4.5) • 123 reviews</span>
                  </div>

                  {/* Price */}
                  <div className="space-y-2">
                    {product.original_price && product.original_price > product.price ? (
                      <div className="flex items-center space-x-3">
                        <span className="text-3xl font-bold text-[#f97316]">
                          {formatPrice(product.price)}
                        </span>
                        <span className="text-xl text-gray-500 line-through">
                          {formatPrice(product.original_price)}
                        </span>
                        <Badge className="bg-red-500 text-white">
                          {discountPercentage}% OFF
                        </Badge>
                      </div>
                    ) : (
                      <span className="text-3xl font-bold text-gray-900">
                        {formatPrice(product.price)}
                      </span>
                    )}
                    
                    {product.unit && (
                      <p className="text-sm text-gray-600">per {product.unit}</p>
                    )}
                  </div>

                  {/* Stock Status */}
                  <div className="flex items-center space-x-2">
                    {product.stock_quantity > 0 ? (
                      <>
                        <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                        <span className="text-green-600 font-medium">In Stock ({product.stock_quantity} available)</span>
                      </>
                    ) : (
                      <>
                        <div className="w-3 h-3 bg-red-500 rounded-full"></div>
                        <span className="text-red-600 font-medium">Out of Stock</span>
                      </>
                    )}
                  </div>

                  {/* MOQ Info */}
                  {product.min_order_quantity && product.min_order_quantity > 1 && (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                      <p className="text-blue-800 text-sm">
                        <strong>Minimum Order Quantity:</strong> {product.min_order_quantity} {product.unit || 'units'}
                      </p>
                    </div>
                  )}

                  {/* Add to Cart Section */}
                  <div className="space-y-4">
                    {product.stock_quantity > 0 ? (
                      <div className="flex items-center space-x-4">
                        {quantityInCart > 0 ? (
                          <div className="flex items-center bg-[#f97316] rounded-lg p-1">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleDecrement}
                              className="h-10 w-10 p-0 text-white hover:bg-white/20"
                            >
                              <Minus className="h-5 w-5" />
                            </Button>
                            
                            <span className="text-white font-medium px-4 text-lg">
                              {quantityInCart}
                            </span>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={handleIncrement}
                              className="h-10 w-10 p-0 text-white hover:bg-white/20"
                            >
                              <Plus className="h-5 w-5" />
                            </Button>
                          </div>
                        ) : (
                          <Button
                            onClick={handleAddToCart}
                            disabled={isLoading}
                            className="bg-[#f97316] hover:bg-[#ea580c] text-white px-8 py-3 text-lg"
                            size="lg"
                          >
                            {isLoading ? (
                              <div className="flex items-center space-x-2">
                                <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                                <span>Adding...</span>
                              </div>
                            ) : (
                              <div className="flex items-center space-x-2">
                                <ShoppingCart className="h-5 w-5" />
                                <span>Add to Cart</span>
                              </div>
                            )}
                          </Button>
                        )}
                        
                        {/* Wishlist and Share buttons */}
                        <Button variant="outline" size="lg" className="p-3">
                          <Heart className="h-5 w-5" />
                        </Button>
                        <Button variant="outline" size="lg" className="p-3">
                          <Share2 className="h-5 w-5" />
                        </Button>
                      </div>
                    ) : (
                      <Button disabled size="lg" className="px-8 py-3 text-lg">
                        Out of Stock
                      </Button>
                    )}
                  </div>

                  {/* Features */}
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Truck className="h-6 w-6 text-[#f97316]" />
                      <div>
                        <p className="font-medium text-gray-900">Free Delivery</p>
                        <p className="text-sm text-gray-600">On orders above ₹500</p>
                      </div>
                    </div>
                    <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                      <Shield className="h-6 w-6 text-[#f97316]" />
                      <div>
                        <p className="font-medium text-gray-900">Quality Assured</p>
                        <p className="text-sm text-gray-600">Premium materials</p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Product Description */}
              {product.description && (
                <Card className="mb-12">
                  <CardContent className="p-6">
                    <h2 className="text-2xl font-bold text-gray-900 mb-4">Product Description</h2>
                    <div className="prose max-w-none text-gray-700">
                      <p>{product.description}</p>
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Similar Products Section */}
              {similarProducts.length > 0 && (
                <div className="mb-12">
                  <h2 className="text-2xl font-bold text-gray-900 mb-6">Similar Products</h2>
                  {isLoadingSimilar ? (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                      {[...Array(4)].map((_, index) => (
                        <div key={index} className="bg-white rounded-lg shadow-sm border p-4">
                          <div className="w-full h-48 bg-gray-200 animate-pulse rounded-lg mb-4"></div>
                          <div className="h-4 bg-gray-200 animate-pulse rounded mb-2"></div>
                          <div className="h-4 bg-gray-200 animate-pulse rounded w-2/3 mb-2"></div>
                          <div className="h-6 bg-gray-200 animate-pulse rounded w-1/3"></div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
                      {similarProducts.map((similarProduct) => (
                        <ProductCard key={similarProduct.id} product={similarProduct} />
                      ))}
                    </div>
                  )}
                </div>
              )}
            </>
          ) : null}
        </div>
      </div>
      <Footer />
    </div>
  );
}
