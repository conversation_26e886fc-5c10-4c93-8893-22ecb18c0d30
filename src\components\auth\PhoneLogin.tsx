'use client'

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Phone, Loader2 } from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';

interface PhoneLoginProps {
  onOTPSent: (phone: string) => void;
  onClose?: () => void;
}

export const PhoneLogin: React.FC<PhoneLoginProps> = ({ onOTPSent, onClose }) => {
  const [phone, setPhone] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const { signInWithPhone } = useAuth();

  const formatPhoneNumber = (value: string) => {
    // Remove all non-digits
    const digits = value.replace(/\D/g, '');
    
    // Limit to 10 digits for Indian phone numbers
    const limitedDigits = digits.slice(0, 10);
    
    return limitedDigits;
  };

  const validatePhoneNumber = (phone: string) => {
    // Indian phone number validation (10 digits, starting with 6-9)
    const phoneRegex = /^[6-9]\d{9}$/;
    return phoneRegex.test(phone);
  };

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const formattedPhone = formatPhoneNumber(e.target.value);
    setPhone(formattedPhone);
    setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validatePhoneNumber(phone)) {
      setError('Please enter a valid 10-digit phone number');
      return;
    }

    setLoading(true);
    setError('');

    try {
      // Format phone number with country code for Supabase
      const formattedPhone = `+91${phone}`;
      const { error } = await signInWithPhone(formattedPhone);
      
      if (error) {
        setError(error.message || 'Failed to send OTP. Please try again.');
      } else {
        onOTPSent(formattedPhone);
      }
    } catch (err) {
      setError('An unexpected error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card className="w-full max-w-md mx-auto">
      <CardHeader className="text-center">
        <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-[#f97316]/10">
          <Phone className="h-6 w-6 text-[#f97316]" />
        </div>
        <CardTitle className="text-2xl font-bold">Welcome to Infratask</CardTitle>
        <CardDescription>
          Enter your phone number to get started
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="phone">Phone Number</Label>
            <div className="relative">
              <div className="absolute left-3 top-1/2 -translate-y-1/2 flex items-center text-gray-500">
                <span className="text-sm">+91</span>
              </div>
              <Input
                id="phone"
                type="tel"
                placeholder="Enter 10-digit phone number"
                value={phone}
                onChange={handlePhoneChange}
                className="pl-12"
                disabled={loading}
                maxLength={10}
              />
            </div>
            {error && (
              <p className="text-sm text-red-600">{error}</p>
            )}
          </div>
          
          <Button 
            type="submit" 
            className="w-full bg-[#f97316] hover:bg-[#ea580c]"
            disabled={loading || !phone || !validatePhoneNumber(phone)}
          >
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Sending OTP...
              </>
            ) : (
              'Send OTP'
            )}
          </Button>
          
          <div className="text-center text-sm text-gray-600">
            By continuing, you agree to our{' '}
            <a href="#" className="text-[#f97316] hover:underline">
              Terms of Service
            </a>{' '}
            and{' '}
            <a href="#" className="text-[#f97316] hover:underline">
              Privacy Policy
            </a>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};
